import React from 'react';
import { BrowserRouter as Router, Routes, Route } from 'react-router-dom';
import ProtectedRoute from './components/ProtectedRoute';
import Header from './components/Header';
import Footer from './components/Footer';
import ScrollToTop from './components/ScrollToTop';
import HomePage from './pages/HomePage';
import ProductsPage from './pages/ProductsPage';
import ProductDetailPage from './pages/ProductDetailPage';
import CartPage from './pages/CartPage';
import CheckoutPage from './pages/CheckoutPage';
import AdminLoginPage from './pages/AdminLoginPage';
import AdminDashboardPage from './pages/AdminDashboardPage';
import AdminProductsPage from './pages/AdminProductsPage';
import AdminCategoriesPage from './pages/AdminCategoriesPage';
import AdminOrdersPage from './pages/AdminOrdersPage';
import AdminCustomersPage from './pages/AdminCustomersPage';
import AdminSettingsPage from './pages/AdminSettingsPage';
import CategoriesPage from './pages/CategoriesPage';

function App() {
  return (
    <div className="min-h-screen bg-white">
      <Router>
        <ScrollToTop />
        <Routes>
          {/* Admin routes - no header/footer */}
          <Route
            path="/admin/login"
            element={<AdminLoginPage />}
          />
          <Route
            path="/admin"
            element={<AdminDashboardPage />}
          />
          <Route
            path="/admin/products"
            element={<AdminProductsPage />}
          />
          <Route
            path="/admin/categories"
            element={<AdminCategoriesPage />}
          />
          <Route
            path="/admin/orders"
            element={<AdminOrdersPage />}
          />
          <Route
            path="/admin/customers"
            element={<AdminCustomersPage />}
          />
          <Route
            path="/admin/settings"
            element={<AdminSettingsPage />}
          />
        
        {/* Public routes - with header/footer */}
        <Route path="/" element={
          <>
            <Header />
            <HomePage />
            <Footer />
          </>
        } />
        <Route path="/products" element={
          <>
            <Header />
            <ProductsPage />
            <Footer />
          </>
        } />
        <Route path="/products/:id" element={
          <>
            <Header />
            <ProductDetailPage />
            <Footer />
          </>
        } />
        <Route path="/cart" element={
          <>
            <Header />
            <CartPage />
            <Footer />
          </>
        } />
        <Route path="/checkout" element={
          <>
            <Header />
            <CheckoutPage />
            <Footer />
          </>
        } />
        <Route path="/categories" element={
          <>
            <Header />
            <CategoriesPage />
            <Footer />
          </>
        } />
          <Route path="*" element={
            <>
              <Header />
              <div className="p-12 text-center text-2xl">404 - Page Not Found</div>
              <Footer />
            </>
          } />
        </Routes>
      </Router>
    </div>
  );
}

export default App;